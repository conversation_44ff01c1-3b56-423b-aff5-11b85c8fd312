{"name": "model-training-platform-client", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check build-only", "preview": "vite preview --port 4173", "test:unit": "vitest --environment jsdom", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@ant-design/icons-vue": "^5.1.8", "ant-design-vue": "^1.7.8", "axios": "^1.7.7", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "echarts": "^5.6.0", "pinia": "^2.0.36", "radash": "^12.1.0", "vue": "^2.7.3", "vue-echarts": "^6.6.8", "vue-router": "^3.6.5"}, "devDependencies": {"@model-training-platform/client-tsconfig": "*", "@rushstack/eslint-patch": "^1.10.4", "@types/jsdom": "^21.1.7", "@types/node": "^18.19.0", "@vitejs/plugin-legacy": "^5.4.2", "@vitejs/plugin-vue2": "^2.3.1", "@vitejs/plugin-vue2-jsx": "^1.1.1", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^1.3.6", "core-js": "^3.38.1", "eslint": "^8.49.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-vue": "^9.17.0", "jsdom": "^20.0.0", "less": "^4.2.0", "markdown-it-anchor": "^9.2.0", "markdown-it-prism": "^3.0.0", "miragejs": "0.2.0-alpha.3", "npm-run-all2": "^6.1.1", "postcss": "^8.4.21", "postinstall-postinstall": "^2.1.0", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "terser": "^5.14.2", "typescript": "~5.3.0", "unocss": "^0.63.4", "vite": "^5.4.9", "vite-plugin-md": "^0.21.5", "vitest": "^2.1.1", "vue-tsc": "^2.1.6"}}