import Vue from 'vue';
import VueRouter from 'vue-router';

Vue.use(VueRouter);

const base = import.meta.env?.BASE_URL ?? '/';

// 懒加载布局组件
const DefaultLayout = () => import('@/layouts/default.layout');
const WorkbenchLayout = () => import('@/layouts/workbench.layout');

const routes = [
  {
    path: '/login',
    component: DefaultLayout,
    children: [
      {
        path: '',
        name: 'login',
        meta: {
          noAuth: true,
          title: '登录',
        },
        component: () => import('@/pages/login'),
      },
    ],
  },
  {
    path: '/',
    component: WorkbenchLayout,
    children: [
      {
        path: '',
        redirect: '/dashboard',
      },
      {
        path: 'dashboard',
        name: 'dashboard',
        meta: {
          title: '仪表盘',
          icon: 'dashboard',
        },
        component: () => import('@/pages/dashboard'),
      },
      {
        path: 'datasets',
        name: 'datasets',
        meta: {
          title: '数据集管理',
          icon: 'database',
        },
        component: () => import('@/pages/datasets'),
      },
      {
        path: 'training',
        name: 'training',
        meta: {
          title: '训练任务',
          icon: 'experiment',
        },
        component: () => import('@/pages/training'),
      },
      {
        path: 'models',
        name: 'models',
        meta: {
          title: '模型管理',
          icon: 'appstore',
        },
        component: () => import('@/pages/models'),
      },
      {
        path: 'deployments',
        name: 'deployments',
        meta: {
          title: '模型部署',
          icon: 'cloud-server',
        },
        component: () => import('@/pages/deployments'),
      },
      {
        path: 'monitoring',
        name: 'monitoring',
        meta: {
          title: '系统监控',
          icon: 'line-chart',
        },
        component: () => import('@/pages/monitoring'),
      },
    ],
  },
  {
    path: '*',
    name: 'not-found',
    meta: {
      noAuth: true,
      title: '页面未找到',
    },
    component: () => import('@/pages/not-found'),
  },
];

const router = new VueRouter({
  mode: 'history',
  base,
  routes,
});

// 路由前置守卫，检查用户是否已登录
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = `${to.meta.title || '模型训练平台'} - 模型训练平台`;

  // 不需要登录的页面（如登录页）直接放行
  if (to.matched.some((record) => record.meta.noAuth)) {
    return next();
  }

  // 检查用户是否已登录
  const user = localStorage.getItem('user');

  if (!user) {
    // 未登录则跳转到登录页
    return next({ path: '/login', query: { redirect: to.fullPath } });
  }

  // 已登录，放行
  return next();
});

// 导出路由实例
export default router;
