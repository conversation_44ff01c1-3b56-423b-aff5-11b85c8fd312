import { Divider, Icon } from 'ant-design-vue';
import { defineComponent } from 'vue';
import { RouterLink, RouterView } from 'vue-router';

import AppHeader from '@/components/common/app-header';

import styles from './workbench.layout.module.less';

const WorkbenchLayout = defineComponent({
  name: 'WorkbenchLayout',
  render() {
    return (
      <div class="flex min-h-full min-w-full flex-col">
        <AppHeader class="z-1" />
        <div class={[styles.container, 'flex-1']}>
          <aside class={styles.aside}>
            <nav class={styles.nav}>
              <div>
                <RouterLink class={styles.item} to="/dashboard">
                  <Icon type="dashboard" />
                  <span>仪表盘</span>
                </RouterLink>
              </div>

              <Divider style={{ margin: '10px 0' }} />

              <div>
                <div class={styles.heading}>数据管理</div>
                <div>
                  <RouterLink class={styles.item} to="/datasets">
                    <Icon type="database" />
                    <span>数据集管理</span>
                  </RouterLink>
                </div>
              </div>

              <Divider style={{ margin: '10px 0' }} />

              <div>
                <div class={styles.heading}>模型训练</div>
                <div>
                  <RouterLink class={styles.item} to="/training">
                    <Icon type="experiment" />
                    <span>训练任务</span>
                  </RouterLink>
                </div>
                <div>
                  <RouterLink class={styles.item} to="/models">
                    <Icon type="appstore" />
                    <span>模型管理</span>
                  </RouterLink>
                </div>
              </div>

              <Divider style={{ margin: '10px 0' }} />

              <div>
                <div class={styles.heading}>模型部署</div>
                <div>
                  <RouterLink class={styles.item} to="/deployments">
                    <Icon type="cloud-server" />
                    <span>模型部署</span>
                  </RouterLink>
                </div>
              </div>

              <Divider style={{ margin: '10px 0' }} />

              <div>
                <div class={styles.heading}>系统监控</div>
                <div>
                  <RouterLink class={styles.item} to="/monitoring">
                    <Icon type="line-chart" />
                    <span>系统监控</span>
                  </RouterLink>
                </div>
              </div>
            </nav>
          </aside>
          <main class={styles.main}>
            <RouterView />
          </main>
        </div>
      </div>
    );
  },
});

export default WorkbenchLayout;
