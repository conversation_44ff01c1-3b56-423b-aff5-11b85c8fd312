<template>
  <a-layout class="workbench-layout">
    <a-layout-sider v-model:collapsed="collapsed" :trigger="null" collapsible class="sidebar">
      <div class="logo">
        <img src="@/assets/logo.png" alt="Logo" v-if="collapsed" />
        <span v-else>模型训练平台</span>
      </div>
      <a-menu v-model:selectedKeys="selectedKeys" theme="dark" mode="inline">
        <a-menu-item key="dashboard" @click="navigateTo('/dashboard')">
          <template #icon>
            <DashboardOutlined />
          </template>
          <span>仪表盘</span>
        </a-menu-item>
        <a-menu-item key="datasets" @click="navigateTo('/datasets')">
          <template #icon>
            <DatabaseOutlined />
          </template>
          <span>数据集管理</span>
        </a-menu-item>
        <a-menu-item key="training" @click="navigateTo('/training')">
          <template #icon>
            <ExperimentOutlined />
          </template>
          <span>训练任务</span>
        </a-menu-item>
        <a-menu-item key="models" @click="navigateTo('/models')">
          <template #icon>
            <AppstoreOutlined />
          </template>
          <span>模型管理</span>
        </a-menu-item>
        <a-menu-item key="deployments" @click="navigateTo('/deployments')">
          <template #icon>
            <CloudServerOutlined />
          </template>
          <span>模型部署</span>
        </a-menu-item>
        <a-menu-item key="monitoring" @click="navigateTo('/monitoring')">
          <template #icon>
            <LineChartOutlined />
          </template>
          <span>系统监控</span>
        </a-menu-item>
      </a-menu>
    </a-layout-sider>
    <a-layout>
      <a-layout-header class="header">
        <div class="header-left">
          <a-button type="text" @click="toggleCollapsed" class="trigger-button">
            <MenuFoldOutlined v-if="collapsed" />
            <MenuUnfoldOutlined v-else />
          </a-button>
          <breadcrumb />
        </div>
        <div class="header-right">
          <a-dropdown>
            <a class="user-dropdown" @click.prevent>
              <a-avatar>
                <template #icon><UserOutlined /></template>
              </a-avatar>
              <span class="username">{{ currentUser?.username || '用户' }}</span>
            </a>
            <template #overlay>
              <a-menu>
                <a-menu-item key="profile">
                  <UserOutlined />
                  个人信息
                </a-menu-item>
                <a-menu-item key="settings">
                  <SettingOutlined />
                  设置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout" @click="handleLogout">
                  <LogoutOutlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>
      <a-layout-content class="content">
        <router-view />
      </a-layout-content>
      <a-layout-footer class="footer"> 模型训练平台 ©2025 Created by Your Company </a-layout-footer>
    </a-layout>
  </a-layout>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  DatabaseOutlined,
  ExperimentOutlined,
  AppstoreOutlined,
  CloudServerOutlined,
  LineChartOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
} from '@ant-design/icons-vue';
import { useUserStore } from '@/store/modules/user.store';
import Breadcrumb from './components/Breadcrumb.vue';

export default defineComponent({
  name: 'WorkbenchLayout',
  components: {
    Breadcrumb,
    MenuFoldOutlined,
    MenuUnfoldOutlined,
    DashboardOutlined,
    DatabaseOutlined,
    ExperimentOutlined,
    AppstoreOutlined,
    CloudServerOutlined,
    LineChartOutlined,
    UserOutlined,
    SettingOutlined,
    LogoutOutlined,
  },
  setup() {
    const userStore = useUserStore();

    const collapsed = ref(false);
    const selectedKeys = ref<string[]>(['dashboard']);

    const currentUser = computed(() => userStore.currentUser);

    return {
      collapsed,
      selectedKeys,
      currentUser,
      userStore,
    };
  },
  methods: {
    toggleCollapsed() {
      this.collapsed = !this.collapsed;
    },

    navigateTo(path: string) {
      this.$router.push(path);
    },

    async handleLogout() {
      try {
        await this.userStore.logout();
        message.success('已成功退出登录');
        this.$router.push('/login');
      } catch (error: any) {
        message.error(error.message || '退出登录失败');
      }
    },
  },
  watch: {
    '$route.path': {
      handler(path: string) {
        const key = path.split('/')[1] || 'dashboard';
        this.selectedKeys = [key];
      },
      immediate: true,
    },
  },
  mounted() {
    // 获取当前用户信息
    this.userStore.fetchCurrentUser();
  },
});
</script>

<style lang="less" scoped>
.workbench-layout {
  min-height: 100vh;
}

.sidebar {
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  z-index: 10;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  font-weight: bold;

  img {
    height: 32px;
  }
}

.header {
  background: #fff;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  z-index: 9;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  padding-right: 24px;
}

.trigger-button {
  padding: 0 24px;
  font-size: 18px;
  cursor: pointer;
  transition: color 0.3s;

  &:hover {
    color: #1890ff;
  }
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;

  .username {
    margin-left: 8px;
  }
}

.content {
  margin: 24px 16px;
  padding: 0;
  background: #fff;
  min-height: 280px;
}

.footer {
  text-align: center;
  color: rgba(0, 0, 0, 0.45);
}
</style>
