import { defineComponent } from 'vue';

export const COMPANY_LISTED_STATUSES = [
  {
    label: '已上市',
    value: 1,
  },
  {
    label: '未上市',
    value: 2,
  },
];

/**
 * 机构类型（对象）
 */
export const COMPANY_LISTED_STATUS_MAP = COMPANY_LISTED_STATUSES.reduce((acc, curr) => {
  acc[curr.value] = curr.label;
  return acc;
}, {});

const CompanyListedStatus = defineComponent({
  name: 'CompanyListedStatus',
  props: {
    status: {
      type: [String, Number],
      required: false,
    },
  },
  render() {
    if (!this.status || !COMPANY_LISTED_STATUS_MAP[this.status]) {
      return <div>-</div>;
    }
    return <div>{COMPANY_LISTED_STATUS_MAP[this.status]}</div>;
  },
});

export default CompanyListedStatus;
