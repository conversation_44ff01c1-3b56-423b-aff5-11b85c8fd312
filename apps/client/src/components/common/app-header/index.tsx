import { defineComponent, ref, computed } from 'vue';
import { Layout, Menu, Dropdown, <PERSON><PERSON>, Badge, Button } from 'ant-design-vue';
import {
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons-vue';
import { useUserStore } from '@/store/modules/user.store';

const { Header } = Layout;

export default defineComponent({
  name: 'AppHeader',
  props: {
    collapsed: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['toggle'],
  setup(props, { emit }) {
    const userStore = useUserStore();
    const notificationCount = ref(0);

    const currentUser = computed(() => userStore.currentUser);

    return {
      userStore,
      notificationCount,
      currentUser,
    };
  },
  methods: {
    // 处理菜单点击
    handleMenuClick({ key }: { key: string }) {
      if (key === 'logout') {
        this.handleLogout();
      } else if (key === 'profile') {
        this.$router.push('/profile');
      } else if (key === 'settings') {
        this.$router.push('/settings');
      }
    },

    // 处理退出登录
    async handleLogout() {
      try {
        await this.userStore.logout();
        this.$router.push('/login');
      } catch (error) {
        console.error('Logout failed:', error);
      }
    },

    // 切换侧边栏折叠状态
    toggleCollapsed() {
      this.$emit('toggle');
    },
  },
  render() {
    return (
      <Header class="app-header">
        <div class="header-left">
          <Button type="text" onClick={this.toggleCollapsed} class="trigger-button">
            {this.collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          </Button>
          <div class="logo">模型训练平台</div>
        </div>
        <div class="header-right">
          <Badge count={this.notificationCount} class="notification-badge">
            <Button type="text" icon={<BellOutlined />} class="notification-button" />
          </Badge>
          <Dropdown
            v-slots={{
              overlay: () => (
                <Menu onClick={this.handleMenuClick}>
                  <Menu.Item key="profile">
                    <UserOutlined />
                    个人信息
                  </Menu.Item>
                  <Menu.Item key="settings">
                    <SettingOutlined />
                    设置
                  </Menu.Item>
                  <Menu.Divider />
                  <Menu.Item key="logout">
                    <LogoutOutlined />
                    退出登录
                  </Menu.Item>
                </Menu>
              ),
            }}
          >
            <div class="user-dropdown">
              <Avatar icon={<UserOutlined />} />
              <span class="username">{this.currentUser?.username || '用户'}</span>
            </div>
          </Dropdown>
        </div>
      </Header>
    );
  },
});
