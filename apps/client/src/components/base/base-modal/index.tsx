import { defineComponent, PropType, ref, watch } from 'vue';
import { Modal } from 'ant-design-vue';

export default defineComponent({
  name: 'BaseModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    width: {
      type: [Number, String],
      default: 520,
    },
    centered: {
      type: Boolean,
      default: false,
    },
    closable: {
      type: Boolean,
      default: true,
    },
    confirmLoading: {
      type: Boolean,
      default: false,
    },
    destroyOnClose: {
      type: Boolean,
      default: false,
    },
    footer: {
      type: [Boolean, Array, Object],
      default: undefined,
    },
    maskClosable: {
      type: Boolean,
      default: true,
    },
    okText: {
      type: String,
      default: '确定',
    },
    okType: {
      type: String as PropType<'primary' | 'danger' | 'dashed' | 'ghost' | 'default' | 'link' | 'text'>,
      default: 'primary',
    },
    cancelText: {
      type: String,
      default: '取消',
    },
    keyboard: {
      type: <PERSON>olean,
      default: true,
    },
    zIndex: {
      type: Number,
      default: 1000,
    },
    bodyStyle: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({}),
    },
  },
  emits: ['update:visible', 'ok', 'cancel'],
  setup(props, { slots, emit }) {
    const localVisible = ref(props.visible);

    // 监听visible属性变化
    watch(
      () => props.visible,
      (val) => {
        localVisible.value = val;
      }
    );

    // 处理确认事件
    const handleOk = (e: Event) => {
      emit('ok', e);
    };

    // 处理取消事件
    const handleCancel = (e: Event) => {
      emit('update:visible', false);
      emit('cancel', e);
    };

    return () => (
      <Modal
        v-model:visible={localVisible.value}
        title={props.title}
        width={props.width}
        centered={props.centered}
        closable={props.closable}
        confirmLoading={props.confirmLoading}
        destroyOnClose={props.destroyOnClose}
        footer={props.footer}
        maskClosable={props.maskClosable}
        okText={props.okText}
        okType={props.okType}
        cancelText={props.cancelText}
        keyboard={props.keyboard}
        zIndex={props.zIndex}
        bodyStyle={props.bodyStyle}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        {slots.default?.()}
        {slots.footer && <template #footer>{slots.footer()}</template>}
        {slots.title && <template #title>{slots.title()}</template>}
      </Modal>
    );
  },
});
