import { defineComponent, PropType, ref, watch } from 'vue';
import { Form } from 'ant-design-vue';

export default defineComponent({
  name: 'BaseForm',
  props: {
    model: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    rules: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
    labelCol: {
      type: Object as PropType<{ span?: number; offset?: number }>,
      default: () => ({ span: 6 }),
    },
    wrapperCol: {
      type: Object as PropType<{ span?: number; offset?: number }>,
      default: () => ({ span: 18 }),
    },
    layout: {
      type: String as PropType<'horizontal' | 'vertical' | 'inline'>,
      default: 'horizontal',
    },
    labelAlign: {
      type: String as PropType<'left' | 'right'>,
      default: 'right',
    },
    hideRequiredMark: {
      type: Boolean,
      default: false,
    },
    colon: {
      type: Boolean,
      default: true,
    },
    validateOnRuleChange: {
      type: Boolean,
      default: true,
    },
    scrollToFirstError: {
      type: [Boolean, Object] as PropType<boolean | Record<string, any>>,
      default: true,
    },
    validateTrigger: {
      type: [String, Array] as PropType<string | string[]>,
      default: 'change',
    },
    name: {
      type: String,
      default: '',
    },
  },
  emits: ['finish', 'finishFailed', 'submit', 'validate'],
  setup(props, { slots, emit, expose }) {
    const formRef = ref();

    // 表单提交成功
    const handleFinish = (values: any) => {
      emit('finish', values);
    };

    // 表单提交失败
    const handleFinishFailed = ({ values, errorFields, outOfDate }: any) => {
      emit('finishFailed', { values, errorFields, outOfDate });
    };

    // 表单验证
    const handleValidate = (...args: any[]) => {
      emit('validate', ...args);
    };

    // 表单提交
    const handleSubmit = (e: Event) => {
      emit('submit', e);
    };

    // 暴露方法
    const validate = () => {
      return formRef.value?.validate();
    };

    const validateField = (nameList: string | string[]) => {
      return formRef.value?.validateField(nameList);
    };

    const resetFields = () => {
      formRef.value?.resetFields();
    };

    const clearValidate = (nameList?: string | string[]) => {
      formRef.value?.clearValidate(nameList);
    };

    const scrollToField = (name: string, options?: Record<string, any>) => {
      formRef.value?.scrollToField(name, options);
    };

    expose({
      validate,
      validateField,
      resetFields,
      clearValidate,
      scrollToField,
      formRef,
    });

    return () => (
      <Form
        ref={formRef}
        model={props.model}
        rules={props.rules}
        labelCol={props.labelCol}
        wrapperCol={props.wrapperCol}
        layout={props.layout}
        labelAlign={props.labelAlign}
        hideRequiredMark={props.hideRequiredMark}
        colon={props.colon}
        validateOnRuleChange={props.validateOnRuleChange}
        scrollToFirstError={props.scrollToFirstError}
        validateTrigger={props.validateTrigger}
        name={props.name}
        onFinish={handleFinish}
        onFinishFailed={handleFinishFailed}
        onValidate={handleValidate}
        onSubmit={handleSubmit}
      >
        {slots.default?.()}
      </Form>
    );
  },
});
