import { defineComponent, PropType } from 'vue';
import { Card } from 'ant-design-vue';

export default defineComponent({
  name: 'BaseCard',
  props: {
    title: {
      type: String,
      default: '',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    bordered: {
      type: Boolean,
      default: true,
    },
    bodyStyle: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({}),
    },
    extra: {
      type: [String, Object],
      default: '',
    },
    size: {
      type: String as PropType<'default' | 'small'>,
      default: 'default',
    },
    hoverable: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, { slots }) {
    return () => (
      <Card
        title={props.title}
        loading={props.loading}
        bordered={props.bordered}
        bodyStyle={props.bodyStyle}
        extra={props.extra}
        size={props.size}
        hoverable={props.hoverable}
      >
        {slots.default?.()}
        {slots.extra && <template #extra>{slots.extra()}</template>}
        {slots.title && <template #title>{slots.title()}</template>}
        {slots.cover && <template #cover>{slots.cover()}</template>}
        {slots.actions && <template #actions>{slots.actions()}</template>}
      </Card>
    );
  },
});
