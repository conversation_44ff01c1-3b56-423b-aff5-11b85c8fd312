import { Checkbox as ACheckbox } from 'ant-design-vue';
import { computed, defineComponent, type VNodeData } from 'vue';

/**
 * BaseBinaryCheckbox
 * 支持0、1值
 */
const BaseBinaryCheckbox = defineComponent({
  name: 'BaseBinaryCheckbox',
  props: {
    /**
     * 值
     */
    value: {
      type: Number,
      default: 0,
    },
  },
  emits: ['change'],
  setup(props, { emit }) {
    const onChange = (e: Event) => {
      const target = e.target as HTMLInputElement | null;
      emit('change', target?.checked ? 1 : 0);
    };
    const booleanValue = computed(() => (props.value === 0 ? false : true));

    return {
      booleanValue,
      onChange,
    };
  },
  render() {
    const context: VNodeData = {
      props: {
        // ...this.$props,
        checked: this.booleanValue,
      },
      on: {
        change: this.onChange,
      },
      attrs: this.$attrs,
    };
    return <ACheckbox {...context}>{this.$slots.default}</ACheckbox>;
  },
});

export default BaseBinaryCheckbox;
