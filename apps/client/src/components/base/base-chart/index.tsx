import { defineComponent, PropType, ref, watch, onMounted, onBeforeUnmount } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON><PERSON> } from 'echarts/charts';
import {
  GridComponent,
  TooltipComponent,
  TitleComponent,
  LegendComponent,
  DataZoomComponent,
  ToolboxComponent,
} from 'echarts/components';
import VChart from 'vue-echarts';

// 注册必要的组件
use([
  <PERSON>vas<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Pie<PERSON>hart,
  Scatter<PERSON>hart,
  GridComponent,
  TooltipComponent,
  TitleComponent,
  LegendComponent,
  DataZoomComponent,
  ToolboxComponent,
]);

export default defineComponent({
  name: 'BaseChart',
  props: {
    option: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    height: {
      type: [String, Number],
      default: '400px',
    },
    width: {
      type: [String, Number],
      default: '100%',
    },
    autoResize: {
      type: Boolean,
      default: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    theme: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const chartRef = ref<any>(null);

    // 处理图表实例
    const handleChartInit = (chart: any) => {
      chartRef.value = chart;
    };

    // 监听option变化，更新图表
    watch(
      () => props.option,
      (newOption) => {
        if (chartRef.value && newOption) {
          chartRef.value.setOption(newOption, true);
        }
      },
      { deep: true }
    );

    // 监听loading状态变化
    watch(
      () => props.loading,
      (loading) => {
        if (chartRef.value) {
          loading ? chartRef.value.showLoading() : chartRef.value.hideLoading();
        }
      }
    );

    // 组件挂载后，如果处于loading状态，显示loading
    onMounted(() => {
      if (props.loading && chartRef.value) {
        chartRef.value.showLoading();
      }
    });

    // 组件卸载前，清除图表实例
    onBeforeUnmount(() => {
      if (chartRef.value) {
        chartRef.value.dispose();
      }
    });

    return () => (
      <div style={{ height: props.height, width: props.width }}>
        <VChart
          ref={chartRef}
          option={props.option}
          autoresize={props.autoResize}
          theme={props.theme}
          onInit={handleChartInit}
          style={{ height: '100%', width: '100%' }}
        />
      </div>
    );
  },
});
