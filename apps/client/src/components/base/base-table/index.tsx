import { defineComponent, PropType, ref, watch } from 'vue';
import { Table } from 'ant-design-vue';
import './base-table.module.less';

export default defineComponent({
  name: 'BaseTable',
  props: {
    columns: {
      type: Array as PropType<any[]>,
      required: true,
    },
    dataSource: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    rowKey: {
      type: [String, Function] as PropType<string | ((record: any) => string)>,
      default: 'id',
    },
    pagination: {
      type: [Object, Boolean] as PropType<Record<string, any> | boolean>,
      default: () => ({
        current: 1,
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total: number) => `共 ${total} 条`,
        pageSizeOptions: ['10', '20', '50', '100'],
      }),
    },
    size: {
      type: String as PropType<'default' | 'middle' | 'small'>,
      default: 'default',
    },
    bordered: {
      type: Boolean,
      default: false,
    },
    scroll: {
      type: Object as PropType<{ x?: number | string | true; y?: number | string }>,
      default: () => ({}),
    },
    rowSelection: {
      type: Object as PropType<any>,
      default: null,
    },
    onChange: {
      type: Function as PropType<(pagination: any, filters: any, sorter: any) => void>,
      default: null,
    },
  },
  emits: ['change', 'update:selectedRowKeys'],
  setup(props, { slots, emit }) {
    const selectedRowKeys = ref<(string | number)[]>([]);

    // 处理表格变化事件
    const handleTableChange = (pagination: any, filters: any, sorter: any) => {
      emit('change', pagination, filters, sorter);
      if (props.onChange) {
        props.onChange(pagination, filters, sorter);
      }
    };

    // 处理行选择变化
    const handleRowSelectionChange = (keys: (string | number)[]) => {
      selectedRowKeys.value = keys;
      emit('update:selectedRowKeys', keys);
    };

    // 构建行选择配置
    const getRowSelection = () => {
      if (!props.rowSelection) return undefined;

      return {
        ...props.rowSelection,
        onChange: handleRowSelectionChange,
        selectedRowKeys: selectedRowKeys.value,
      };
    };

    // 监听外部传入的selectedRowKeys变化
    watch(
      () => props.rowSelection?.selectedRowKeys,
      (newKeys) => {
        if (newKeys) {
          selectedRowKeys.value = newKeys;
        }
      },
      { immediate: true }
    );

    return () => (
      <div class="base-table">
        <Table
          columns={props.columns}
          dataSource={props.dataSource}
          loading={props.loading}
          rowKey={props.rowKey}
          pagination={props.pagination}
          size={props.size}
          bordered={props.bordered}
          scroll={props.scroll}
          rowSelection={getRowSelection()}
          onChange={handleTableChange}
          v-slots={slots}
        />
      </div>
    );
  },
});
