import { defineComponent, PropType } from 'vue';
import { Popconfirm } from 'ant-design-vue';

export default defineComponent({
  name: 'BasePopconfirm',
  props: {
    title: {
      type: String,
      required: true,
    },
    okText: {
      type: String,
      default: '确定',
    },
    cancelText: {
      type: String,
      default: '取消',
    },
    okType: {
      type: String as PropType<'primary' | 'danger' | 'dashed' | 'ghost' | 'default' | 'link' | 'text'>,
      default: 'primary',
    },
    okButtonProps: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
    cancelButtonProps: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
    placement: {
      type: String as PropType<
        | 'top'
        | 'left'
        | 'right'
        | 'bottom'
        | 'topLeft'
        | 'topRight'
        | 'bottomLeft'
        | 'bottomRight'
        | 'leftTop'
        | 'leftBottom'
        | 'rightTop'
        | 'rightBottom'
      >,
      default: 'top',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    overlayClassName: {
      type: String,
      default: '',
    },
    overlayStyle: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({}),
    },
    icon: {
      type: Object,
      default: null,
    },
  },
  emits: ['confirm', 'cancel', 'visibleChange'],
  setup(props, { slots, emit }) {
    // 处理确认事件
    const handleConfirm = (e: Event) => {
      emit('confirm', e);
    };

    // 处理取消事件
    const handleCancel = (e: Event) => {
      emit('cancel', e);
    };

    // 处理可见性变化事件
    const handleVisibleChange = (visible: boolean) => {
      emit('visibleChange', visible);
    };

    return () => (
      <Popconfirm
        title={props.title}
        okText={props.okText}
        cancelText={props.cancelText}
        okType={props.okType}
        okButtonProps={props.okButtonProps}
        cancelButtonProps={props.cancelButtonProps}
        placement={props.placement}
        disabled={props.disabled}
        overlayClassName={props.overlayClassName}
        overlayStyle={props.overlayStyle}
        icon={props.icon}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        onVisibleChange={handleVisibleChange}
      >
        {slots.default?.()}
        {slots.title && <template #title>{slots.title()}</template>}
        {slots.icon && <template #icon>{slots.icon()}</template>}
      </Popconfirm>
    );
  },
});
