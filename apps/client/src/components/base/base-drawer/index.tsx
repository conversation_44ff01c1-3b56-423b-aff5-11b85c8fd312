import { defineComponent, PropType, ref, watch } from 'vue';
import { Drawer } from 'ant-design-vue';

export default defineComponent({
  name: 'BaseDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    width: {
      type: [Number, String],
      default: 378,
    },
    height: {
      type: [Number, String],
      default: 378,
    },
    placement: {
      type: String as PropType<'top' | 'right' | 'bottom' | 'left'>,
      default: 'right',
    },
    closable: {
      type: Boolean,
      default: true,
    },
    destroyOnClose: {
      type: Boolean,
      default: false,
    },
    maskClosable: {
      type: Boolean,
      default: true,
    },
    mask: {
      type: Boolean,
      default: true,
    },
    zIndex: {
      type: Number,
      default: 1000,
    },
    bodyStyle: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({}),
    },
    headerStyle: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({}),
    },
    drawerStyle: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({}),
    },
    footerStyle: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({}),
    },
    size: {
      type: String as PropType<'default' | 'large'>,
      default: 'default',
    },
    extra: {
      type: [String, Object],
      default: '',
    },
    footer: {
      type: [Boolean, Object],
      default: undefined,
    },
  },
  emits: ['update:visible', 'close'],
  setup(props, { slots, emit }) {
    const localVisible = ref(props.visible);

    // 监听visible属性变化
    watch(
      () => props.visible,
      (val) => {
        localVisible.value = val;
      }
    );

    // 处理关闭事件
    const handleClose = (e: Event) => {
      emit('update:visible', false);
      emit('close', e);
    };

    return () => (
      <Drawer
        v-model:visible={localVisible.value}
        title={props.title}
        width={props.width}
        height={props.height}
        placement={props.placement}
        closable={props.closable}
        destroyOnClose={props.destroyOnClose}
        maskClosable={props.maskClosable}
        mask={props.mask}
        zIndex={props.zIndex}
        bodyStyle={props.bodyStyle}
        headerStyle={props.headerStyle}
        drawerStyle={props.drawerStyle}
        footerStyle={props.footerStyle}
        size={props.size}
        extra={props.extra}
        footer={props.footer}
        onClose={handleClose}
      >
        {slots.default?.()}
        {slots.footer && <template #footer>{slots.footer()}</template>}
        {slots.title && <template #title>{slots.title()}</template>}
        {slots.extra && <template #extra>{slots.extra()}</template>}
      </Drawer>
    );
  },
});
