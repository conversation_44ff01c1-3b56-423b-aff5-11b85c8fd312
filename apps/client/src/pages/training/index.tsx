import { defineComponent, ref, onMounted, computed } from 'vue';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Tooltip,
  Modal,
  Row,
  Col,
  Typography,
  Progress,
  Statistic,
} from 'ant-design-vue';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  StopOutlined,
  ExperimentOutlined,
  LineChartOutlined,
} from '@ant-design/icons-vue';
import { useTrainingStore } from '@/store/modules/training.store';

const { Title, Text } = Typography;
const { Option } = Select;

export default defineComponent({
  name: 'TrainingPage',
  setup() {
    const trainingStore = useTrainingStore();
    const createModalVisible = ref(false);
    const detailModalVisible = ref(false);
    const selectedTrainingJob = ref(null);
    const searchText = ref('');
    const selectedStatus = ref('');
    const selectedModelType = ref('');

    // 表格列定义
    const columns = [
      {
        title: '任务名称',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        ellipsis: true,
      },
      {
        title: '数据集',
        dataIndex: 'datasetId',
        key: 'datasetId',
        width: 150,
        ellipsis: true,
      },
      {
        title: '模型类型',
        dataIndex: 'modelType',
        key: 'modelType',
        width: 120,
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
      },
      {
        title: '进度',
        key: 'progress',
        width: 120,
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 150,
      },
      {
        title: '运行时间',
        key: 'duration',
        width: 120,
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right',
      },
    ];

    // 状态选项
    const statusOptions = [
      { value: 'PENDING', label: '待启动' },
      { value: 'RUNNING', label: '运行中' },
      { value: 'COMPLETED', label: '已完成' },
      { value: 'FAILED', label: '失败' },
      { value: 'STOPPED', label: '已停止' },
    ];

    // 模型类型选项
    const modelTypes = [
      { value: 'TECH_HEALTH', label: '科创健康性模型' },
      { value: 'INTERNAL_CONTROL_RISK', label: '内控风险模型' },
      { value: 'CREDIT_RISK', label: '信用风险模型' },
      { value: 'MARKET_RISK', label: '市场风险模型' },
    ];

    // 计算属性
    const filteredTrainingJobs = computed(() => {
      let jobs = trainingStore.trainingJobs;

      if (searchText.value) {
        jobs = jobs.filter(
          (job) =>
            job.name.toLowerCase().includes(searchText.value.toLowerCase()) ||
            job.description.toLowerCase().includes(searchText.value.toLowerCase())
        );
      }

      if (selectedStatus.value) {
        jobs = jobs.filter((job) => job.status === selectedStatus.value);
      }

      if (selectedModelType.value) {
        jobs = jobs.filter((job) => job.modelType === selectedModelType.value);
      }

      return jobs;
    });

    // 统计数据
    const statistics = computed(() => {
      const jobs = trainingStore.trainingJobs;
      return {
        total: jobs.length,
        running: jobs.filter((job) => job.status === 'RUNNING').length,
        completed: jobs.filter((job) => job.status === 'COMPLETED').length,
        failed: jobs.filter((job) => job.status === 'FAILED').length,
      };
    });

    // 方法
    const handleSearch = () => {
      fetchTrainingJobs();
    };

    const handleReset = () => {
      searchText.value = '';
      selectedStatus.value = '';
      selectedModelType.value = '';
      fetchTrainingJobs();
    };

    const fetchTrainingJobs = async () => {
      try {
        await trainingStore.fetchTrainingJobs(trainingStore.currentPage, trainingStore.pageSize, {
          search: searchText.value,
          status: selectedStatus.value,
          modelType: selectedModelType.value,
        });
      } catch (error) {
        console.error('获取训练任务列表失败:', error);
      }
    };

    const showCreateModal = () => {
      createModalVisible.value = true;
    };

    const showDetailModal = (job) => {
      selectedTrainingJob.value = job;
      detailModalVisible.value = true;
    };

    const handleStart = async (job) => {
      try {
        await trainingStore.startTrainingJob(job.id);
        await fetchTrainingJobs();
      } catch (error) {
        console.error('启动训练任务失败:', error);
      }
    };

    const handleStop = async (job) => {
      Modal.confirm({
        title: '确认停止',
        content: `确定要停止训练任务 "${job.name}" 吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            await trainingStore.stopTrainingJob(job.id);
            await fetchTrainingJobs();
          } catch (error) {
            console.error('停止训练任务失败:', error);
          }
        },
      });
    };

    const getStatusColor = (status) => {
      const statusMap = {
        PENDING: 'default',
        RUNNING: 'processing',
        COMPLETED: 'success',
        FAILED: 'error',
        STOPPED: 'warning',
      };
      return statusMap[status] || 'default';
    };

    const getStatusText = (status) => {
      const statusMap = {
        PENDING: '待启动',
        RUNNING: '运行中',
        COMPLETED: '已完成',
        FAILED: '失败',
        STOPPED: '已停止',
      };
      return statusMap[status] || status;
    };

    const calculateDuration = (startedAt, completedAt) => {
      if (!startedAt) return '-';
      const start = new Date(startedAt);
      const end = completedAt ? new Date(completedAt) : new Date();
      const duration = Math.floor((end.getTime() - start.getTime()) / 1000);

      const hours = Math.floor(duration / 3600);
      const minutes = Math.floor((duration % 3600) / 60);
      const seconds = duration % 60;

      if (hours > 0) {
        return `${hours}h ${minutes}m ${seconds}s`;
      } else if (minutes > 0) {
        return `${minutes}m ${seconds}s`;
      } else {
        return `${seconds}s`;
      }
    };

    const getProgress = (job) => {
      if (job.status === 'COMPLETED') return 100;
      if (job.status === 'FAILED' || job.status === 'STOPPED') return 0;
      if (job.status === 'RUNNING') {
        // 模拟进度，实际应该从后端获取
        return Math.floor(Math.random() * 80) + 10;
      }
      return 0;
    };

    // 生命周期
    onMounted(() => {
      fetchTrainingJobs();
    });

    return () => (
      <div class="p-6">
        <div class="mb-6">
          <Title level={2}>
            <ExperimentOutlined class="mr-2" />
            训练任务
          </Title>
          <Text type="secondary">管理机器学习模型训练任务，监控训练进度和性能指标</Text>
        </div>

        {/* 统计卡片 */}
        <Row gutter={16} class="mb-4">
          <Col span={6}>
            <Card>
              <Statistic title="总任务数" value={statistics.value.total} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="运行中" value={statistics.value.running} valueStyle={{ color: '#1890ff' }} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="已完成" value={statistics.value.completed} valueStyle={{ color: '#52c41a' }} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="失败" value={statistics.value.failed} valueStyle={{ color: '#f5222d' }} />
            </Card>
          </Col>
        </Row>

        {/* 搜索和筛选区域 */}
        <Card class="mb-4">
          <Row gutter={16} align="middle">
            <Col span={6}>
              <Input
                value={searchText.value}
                onChange={(e) => (searchText.value = e.target.value)}
                placeholder="搜索任务名称或描述"
                prefix={<SearchOutlined />}
                allowClear
              />
            </Col>
            <Col span={4}>
              <Select
                value={selectedStatus.value}
                onChange={(value) => (selectedStatus.value = value)}
                placeholder="选择状态"
                allowClear
                style={{ width: '100%' }}
              >
                {statusOptions.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={4}>
              <Select
                value={selectedModelType.value}
                onChange={(value) => (selectedModelType.value = value)}
                placeholder="选择模型类型"
                allowClear
                style={{ width: '100%' }}
              >
                {modelTypes.map((type) => (
                  <Option key={type.value} value={type.value}>
                    {type.label}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <Space>
                <Button type="primary" onClick={handleSearch} icon={<SearchOutlined />}>
                  搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Space>
            </Col>
            <Col span={4} class="text-right">
              <Button type="primary" onClick={showCreateModal} icon={<PlusOutlined />}>
                创建任务
              </Button>
            </Col>
          </Row>
        </Card>

        {/* 训练任务列表 */}
        <Card>
          <Table
            dataSource={filteredTrainingJobs.value}
            columns={columns}
            loading={trainingStore.loading}
            pagination={{
              current: trainingStore.currentPage,
              pageSize: trainingStore.pageSize,
              total: trainingStore.totalCount,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, pageSize) => {
                trainingStore.fetchTrainingJobs(page, pageSize, {
                  search: searchText.value,
                  status: selectedStatus.value,
                  modelType: selectedModelType.value,
                });
              },
            }}
            scroll={{ x: 1200 }}
            scopedSlots={{
              status: (text) => <Tag color={getStatusColor(text)}>{getStatusText(text)}</Tag>,
              progress: (text, record) => (
                <Progress
                  percent={getProgress(record)}
                  size="small"
                  status={record.status === 'FAILED' ? 'exception' : 'normal'}
                />
              ),
              createdAt: (text) => new Date(text).toLocaleString(),
              duration: (text, record) => calculateDuration(record.startedAt, record.completedAt),
              action: (text, record) => (
                <Space>
                  <Tooltip title="查看详情">
                    <Button type="link" size="small" icon={<EyeOutlined />} onClick={() => showDetailModal(record)} />
                  </Tooltip>
                  {record.status === 'PENDING' && (
                    <Tooltip title="启动">
                      <Button type="link" size="small" icon={<PlayCircleOutlined />} onClick={() => handleStart(record)} />
                    </Tooltip>
                  )}
                  {record.status === 'RUNNING' && (
                    <Tooltip title="停止">
                      <Button type="link" size="small" danger icon={<StopOutlined />} onClick={() => handleStop(record)} />
                    </Tooltip>
                  )}
                  <Tooltip title="查看指标">
                    <Button type="link" size="small" icon={<LineChartOutlined />} disabled={record.status === 'PENDING'} />
                  </Tooltip>
                </Space>
              ),
            }}
          />
        </Card>
      </div>
    );
  },
});
