import { defineComponent, ref, onMounted, computed } from 'vue';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Tooltip,
  Modal,
  Row,
  Col,
  Typography,
  Statistic,
  Checkbox,
  Descriptions,
  Divider,
} from 'ant-design-vue';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  DownloadOutlined,
  DeleteOutlined,
  AppstoreOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  BarChartOutlined,
} from '@ant-design/icons-vue';
import { useModelStore } from '@/store/modules/model.store';

const { Title, Text } = Typography;
const { Option } = Select;

export default defineComponent({
  name: 'ModelsPage',
  setup() {
    const modelStore = useModelStore();
    const detailModalVisible = ref(false);
    const compareModalVisible = ref(false);
    const selectedModel = ref(null);
    const selectedModels = ref([]);
    const searchText = ref('');
    const selectedStatus = ref('');
    const selectedModelType = ref('');

    // 表格列定义
    const columns = [
      {
        title: '模型名称',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        ellipsis: true,
      },
      {
        title: '版本',
        dataIndex: 'version',
        key: 'version',
        width: 100,
      },
      {
        title: '模型类型',
        dataIndex: 'modelType',
        key: 'modelType',
        width: 120,
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
      },
      {
        title: '准确率',
        key: 'accuracy',
        width: 100,
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 150,
      },
      {
        title: '创建者',
        dataIndex: 'createdBy',
        key: 'createdBy',
        width: 120,
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right',
      },
    ];

    // 状态选项
    const statusOptions = [
      { value: 'DRAFT', label: '草稿' },
      { value: 'PENDING_APPROVAL', label: '待审批' },
      { value: 'APPROVED', label: '已批准' },
      { value: 'REJECTED', label: '已拒绝' },
      { value: 'DEPLOYED', label: '已部署' },
    ];

    // 模型类型选项
    const modelTypes = [
      { value: 'TECH_HEALTH', label: '科创健康性模型' },
      { value: 'INTERNAL_CONTROL_RISK', label: '内控风险模型' },
      { value: 'CREDIT_RISK', label: '信用风险模型' },
      { value: 'MARKET_RISK', label: '市场风险模型' },
    ];

    // 计算属性
    const filteredModels = computed(() => {
      let models = modelStore.models;

      if (searchText.value) {
        models = models.filter(
          (model) =>
            model.name.toLowerCase().includes(searchText.value.toLowerCase()) ||
            model.description.toLowerCase().includes(searchText.value.toLowerCase())
        );
      }

      if (selectedStatus.value) {
        models = models.filter((model) => model.status === selectedStatus.value);
      }

      if (selectedModelType.value) {
        models = models.filter((model) => model.modelType === selectedModelType.value);
      }

      return models;
    });

    // 统计数据
    const statistics = computed(() => {
      const models = modelStore.models;
      return {
        total: models.length,
        approved: models.filter((model) => model.status === 'APPROVED').length,
        deployed: models.filter((model) => model.status === 'DEPLOYED').length,
        pending: models.filter((model) => model.status === 'PENDING_APPROVAL').length,
      };
    });

    // 方法
    const handleSearch = () => {
      fetchModels();
    };

    const handleReset = () => {
      searchText.value = '';
      selectedStatus.value = '';
      selectedModelType.value = '';
      fetchModels();
    };

    const fetchModels = async () => {
      try {
        await modelStore.fetchModels(modelStore.currentPage, modelStore.pageSize, {
          search: searchText.value,
          status: selectedStatus.value,
          modelType: selectedModelType.value,
        });
      } catch (error) {
        console.error('获取模型列表失败:', error);
      }
    };

    const showDetailModal = (model) => {
      selectedModel.value = model;
      detailModalVisible.value = true;
    };

    const showCompareModal = () => {
      if (selectedModels.value.length < 2) {
        Modal.warning({
          title: '提示',
          content: '请至少选择两个模型进行比较',
        });
        return;
      }
      compareModalVisible.value = true;
    };

    const handleApprove = async (model) => {
      Modal.confirm({
        title: '确认批准',
        content: `确定要批准模型 "${model.name}" 吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            await modelStore.approveModel(model.id, {});
            await fetchModels();
          } catch (error) {
            console.error('批准模型失败:', error);
          }
        },
      });
    };

    const handleDownload = async (model, format = 'pkl') => {
      try {
        await modelStore.downloadModel(model.id, format);
      } catch (error) {
        console.error('下载模型失败:', error);
      }
    };

    const handleDelete = (model) => {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除模型 "${model.name}" 吗？此操作不可恢复。`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            await modelStore.deleteModel(model.id);
            await fetchModels();
          } catch (error) {
            console.error('删除模型失败:', error);
          }
        },
      });
    };

    const getStatusColor = (status) => {
      const statusMap = {
        DRAFT: 'default',
        PENDING_APPROVAL: 'warning',
        APPROVED: 'success',
        REJECTED: 'error',
        DEPLOYED: 'processing',
      };
      return statusMap[status] || 'default';
    };

    const getStatusText = (status) => {
      const statusMap = {
        DRAFT: '草稿',
        PENDING_APPROVAL: '待审批',
        APPROVED: '已批准',
        REJECTED: '已拒绝',
        DEPLOYED: '已部署',
      };
      return statusMap[status] || status;
    };

    const getStatusIcon = (status) => {
      const iconMap = {
        DRAFT: <ClockCircleOutlined />,
        PENDING_APPROVAL: <ExclamationCircleOutlined />,
        APPROVED: <CheckCircleOutlined />,
        REJECTED: <ExclamationCircleOutlined />,
        DEPLOYED: <CheckCircleOutlined />,
      };
      return iconMap[status] || null;
    };

    const formatAccuracy = (metrics) => {
      if (!metrics || !metrics.accuracy) return '-';
      return `${(metrics.accuracy * 100).toFixed(2)}%`;
    };

    // 生命周期
    onMounted(() => {
      fetchModels();
    });

    return () => (
      <div class="p-6">
        <div class="mb-6">
          <Title level={2}>
            <AppstoreOutlined class="mr-2" />
            模型管理
          </Title>
          <Text type="secondary">管理机器学习模型，支持版本控制、性能比较和审批流程</Text>
        </div>

        {/* 统计卡片 */}
        <Row gutter={16} class="mb-4">
          <Col span={6}>
            <Card>
              <Statistic title="总模型数" value={statistics.value.total} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="已批准" value={statistics.value.approved} valueStyle={{ color: '#52c41a' }} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="已部署" value={statistics.value.deployed} valueStyle={{ color: '#1890ff' }} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="待审批" value={statistics.value.pending} valueStyle={{ color: '#faad14' }} />
            </Card>
          </Col>
        </Row>

        {/* 搜索和筛选区域 */}
        <Card class="mb-4">
          <Row gutter={16} align="middle">
            <Col span={6}>
              <Input
                value={searchText.value}
                onChange={(e) => (searchText.value = e.target.value)}
                placeholder="搜索模型名称或描述"
                prefix={<SearchOutlined />}
                allowClear
              />
            </Col>
            <Col span={4}>
              <Select
                value={selectedStatus.value}
                onChange={(value) => (selectedStatus.value = value)}
                placeholder="选择状态"
                allowClear
                style={{ width: '100%' }}
              >
                {statusOptions.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={4}>
              <Select
                value={selectedModelType.value}
                onChange={(value) => (selectedModelType.value = value)}
                placeholder="选择模型类型"
                allowClear
                style={{ width: '100%' }}
              >
                {modelTypes.map((type) => (
                  <Option key={type.value} value={type.value}>
                    {type.label}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <Space>
                <Button type="primary" onClick={handleSearch} icon={<SearchOutlined />}>
                  搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
                <Button onClick={showCompareModal} icon={<BarChartOutlined />} disabled={selectedModels.value.length < 2}>
                  比较模型
                </Button>
              </Space>
            </Col>
            <Col span={4} class="text-right">
              <Button type="primary" icon={<PlusOutlined />}>
                注册模型
              </Button>
            </Col>
          </Row>
        </Card>

        {/* 模型列表 */}
        <Card>
          <Table
            dataSource={filteredModels.value}
            columns={columns}
            loading={modelStore.loading}
            rowSelection={{
              selectedRowKeys: selectedModels.value,
              onChange: (selectedRowKeys) => {
                selectedModels.value = selectedRowKeys;
              },
            }}
            pagination={{
              current: modelStore.currentPage,
              pageSize: modelStore.pageSize,
              total: modelStore.totalCount,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, pageSize) => {
                modelStore.fetchModels(page, pageSize, {
                  search: searchText.value,
                  status: selectedStatus.value,
                  modelType: selectedModelType.value,
                });
              },
            }}
            scroll={{ x: 1200 }}
            scopedSlots={{
              status: (text) => (
                <Tag color={getStatusColor(text)} icon={getStatusIcon(text)}>
                  {getStatusText(text)}
                </Tag>
              ),
              accuracy: (text, record) => formatAccuracy(record.metrics),
              createdAt: (text) => new Date(text).toLocaleString(),
              action: (text, record) => (
                <Space>
                  <Tooltip title="查看详情">
                    <Button type="link" size="small" icon={<EyeOutlined />} onClick={() => showDetailModal(record)} />
                  </Tooltip>
                  {record.status === 'PENDING_APPROVAL' && (
                    <Tooltip title="批准">
                      <Button type="link" size="small" icon={<CheckCircleOutlined />} onClick={() => handleApprove(record)} />
                    </Tooltip>
                  )}
                  <Tooltip title="下载">
                    <Button type="link" size="small" icon={<DownloadOutlined />} onClick={() => handleDownload(record)} />
                  </Tooltip>
                  <Tooltip title="删除">
                    <Button type="link" size="small" danger icon={<DeleteOutlined />} onClick={() => handleDelete(record)} />
                  </Tooltip>
                </Space>
              ),
            }}
          />
        </Card>
      </div>
    );
  },
});
