<template>
  <a-modal
    v-model:visible="visible"
    :title="dataset ? `数据集详情: ${dataset.name}` : '数据集详情'"
    width="900px"
    :footer="null"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading">
      <template v-if="dataset">
        <a-descriptions bordered :column="2">
          <a-descriptions-item label="数据集名称" :span="2">
            {{ dataset.name }}
          </a-descriptions-item>
          <a-descriptions-item label="描述" :span="2">
            {{ dataset.description || '无' }}
          </a-descriptions-item>
          <a-descriptions-item label="模型类型">
            <a-tag :color="getModelTypeColor(dataset.modelType)">
              {{ getModelTypeName(dataset.modelType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(dataset.status)">
              {{ getStatusName(dataset.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="文件大小">
            {{ formatFileSize(dataset.fileSize) }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDate(dataset.createdAt) }}
          </a-descriptions-item>
          <a-descriptions-item label="行数">
            {{ dataset.rowCount }}
          </a-descriptions-item>
          <a-descriptions-item label="列数">
            {{ dataset.columnCount }}
          </a-descriptions-item>
          <a-descriptions-item label="标签" :span="2">
            <template v-if="dataset.tags && dataset.tags.length">
              <a-tag v-for="tag in dataset.tags" :key="tag" color="blue">{{ tag }}</a-tag>
            </template>
            <template v-else>无</template>
          </a-descriptions-item>
        </a-descriptions>

        <a-divider />

        <!-- 数据模式可视化 -->
        <a-collapse v-model:activeKey="activeKeys">
          <a-collapse-panel key="schema" header="数据模式">
            <schema-visualization :schema="dataset.schema" />
          </a-collapse-panel>
        </a-collapse>

        <a-divider />

        <!-- 数据预览 -->
        <div class="preview-header">
          <h3>数据预览</h3>
          <a-pagination
            v-model:current="currentPage"
            :pageSize="pageSize"
            :total="dataset.rowCount"
            size="small"
            @change="handlePageChange"
            :showSizeChanger="true"
            :pageSizeOptions="['10', '20', '50', '100']"
            @showSizeChange="handlePageSizeChange"
            showQuickJumper
          />
        </div>

        <div class="preview-table-container">
          <a-table
            v-if="previewData && previewData.columns && previewData.data"
            :columns="previewColumns"
            :data-source="previewData.data"
            :pagination="false"
            size="small"
            :scroll="{ x: '100%', y: 300 }"
          >
            <template #headerCell="{ column }">
              <template v-if="previewData.fieldDescriptions && previewData.fieldDescriptions[column.dataIndex]">
                <span>
                  {{ column.title }}
                  <field-description-tooltip :description="previewData.fieldDescriptions[column.dataIndex]" />
                </span>
              </template>
              <template v-else>
                {{ column.title }}
              </template>
            </template>
          </a-table>
          <a-empty v-else description="暂无预览数据" />
        </div>
      </template>
      <a-empty v-else description="数据集不存在或已被删除" />
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits, watch, computed } from 'vue';
import { useDatasetStore } from '@/store/modules/dataset.store';
import { formatDate } from '@/utils/date.formatter';
import SchemaVisualization from './SchemaVisualization.vue';
import FieldDescriptionTooltip from './FieldDescriptionTooltip.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  datasetId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:visible']);

const datasetStore = useDatasetStore();
const loading = ref(false);
const dataset = ref<any>(null);
const previewData = ref<any>(null);
const activeKeys = ref<string[]>(['schema']);
const currentPage = ref(1);
const pageSize = ref(10);

// 监听 visible 和 datasetId 变化
watch(
  () => [props.visible, props.datasetId],
  async ([visible, datasetId]) => {
    if (visible && datasetId) {
      await loadDataset(datasetId);
      await loadPreviewData(datasetId);
    }
  },
  { immediate: true }
);

// 加载数据集详情
const loadDataset = async (id: string) => {
  loading.value = true;
  try {
    dataset.value = await datasetStore.fetchDatasetById(id);
  } catch (error) {
    console.error('获取数据集详情失败', error);
  } finally {
    loading.value = false;
  }
};

// 加载预览数据
const loadPreviewData = async (id: string) => {
  loading.value = true;
  try {
    const skip = (currentPage.value - 1) * pageSize.value;
    const limit = pageSize.value;
    previewData.value = await datasetStore.getDatasetPreview(id, limit);
  } catch (error) {
    console.error('获取数据集预览失败', error);
  } finally {
    loading.value = false;
  }
};

// 处理分页变化
const handlePageChange = (page: number) => {
  currentPage.value = page;
  loadPreviewData(props.datasetId);
};

// 处理每页条数变化
const handlePageSizeChange = (current: number, size: number) => {
  pageSize.value = size;
  loadPreviewData(props.datasetId);
};

// 关闭对话框
const handleCancel = () => {
  emit('update:visible', false);
  dataset.value = null;
  previewData.value = null;
  currentPage.value = 1;
  pageSize.value = 10;
};

// 预览数据表格列
const previewColumns = computed(() => {
  if (!previewData.value || !previewData.value.columns) return [];

  return previewData.value.columns.map((col: string) => ({
    title: col,
    dataIndex: col,
    key: col,
    ellipsis: true,
    width: 150,
  }));
});

// 辅助函数
const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  }
};

const getModelTypeName = (type: string) => {
  const types = {
    health: '科创健康性模型',
    risk: '内控风险模型',
  };
  return types[type] || type;
};

const getModelTypeColor = (type: string) => {
  const colors = {
    health: 'green',
    risk: 'orange',
  };
  return colors[type] || 'blue';
};

const getStatusName = (status: string) => {
  const statuses = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    failed: '失败',
  };
  return statuses[status] || status;
};

const getStatusColor = (status: string) => {
  const colors = {
    pending: 'blue',
    processing: 'processing',
    completed: 'success',
    failed: 'error',
  };
  return colors[status] || 'default';
};
</script>

<style scoped>
.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.preview-table-container {
  margin-top: 16px;
  overflow: auto;
}
</style>
