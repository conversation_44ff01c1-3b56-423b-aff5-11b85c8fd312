<template>
  <div class="schema-visualization">
    <a-empty v-if="!schema || Object.keys(schema).length === 0" description="无模式数据" />
    <template v-else>
      <div class="schema-header">
        <h4>数据模式可视化</h4>
        <a-radio-group v-model:value="viewMode" button-style="solid" size="small">
          <a-radio-button value="table">表格视图</a-radio-button>
          <a-radio-button value="tree">树形视图</a-radio-button>
        </a-radio-group>
      </div>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'" class="table-view">
        <a-table :columns="columns" :data-source="tableData" :pagination="false" size="small" :scroll="{ y: 300 }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'required'">
              <a-tag v-if="record.required" color="red">必填</a-tag>
              <a-tag v-else color="green">可选</a-tag>
            </template>
            <template v-if="column.key === 'constraints'">
              <div v-if="record.constraints && record.constraints.length > 0">
                <a-tag v-for="(constraint, index) in record.constraints" :key="index" color="blue">
                  {{ constraint }}
                </a-tag>
              </div>
              <span v-else>无</span>
            </template>
          </template>
        </a-table>
      </div>

      <!-- 树形视图 -->
      <div v-else-if="viewMode === 'tree'" class="tree-view">
        <a-tree :tree-data="treeData" :default-expanded-keys="['root']" :show-icon="true">
          <template #title="{ title, dataRef }">
            <span>
              {{ title }}
              <a-tag v-if="dataRef.required" color="red" style="margin-left: 8px">必填</a-tag>
              <a-tag v-if="dataRef.type" color="blue" style="margin-left: 8px">{{ dataRef.type }}</a-tag>
            </span>
          </template>
          <template #icon="{ dataRef }">
            <field-string-outlined v-if="dataRef.type === 'string'" />
            <number-outlined v-else-if="dataRef.type === 'number'" />
            <calendar-outlined v-else-if="dataRef.type === 'date'" />
            <check-square-outlined v-else-if="dataRef.type === 'boolean'" />
            <apartment-outlined v-else-if="dataRef.type === 'object'" />
            <unordered-list-outlined v-else-if="dataRef.type === 'array'" />
            <file-outlined v-else />
          </template>
        </a-tree>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, defineProps } from 'vue';
import {
  FieldStringOutlined,
  NumberOutlined,
  CalendarOutlined,
  CheckSquareOutlined,
  ApartmentOutlined,
  UnorderedListOutlined,
  FileOutlined,
} from '@ant-design/icons-vue';

const props = defineProps({
  schema: {
    type: Object,
    default: () => ({}),
  },
});

const viewMode = ref('table');

// 表格视图数据
const columns = [
  {
    title: '字段名',
    dataIndex: 'name',
    key: 'name',
    width: 150,
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100,
  },
  {
    title: '是否必填',
    dataIndex: 'required',
    key: 'required',
    width: 100,
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },
  {
    title: '约束条件',
    dataIndex: 'constraints',
    key: 'constraints',
  },
];

const tableData = computed(() => {
  if (!props.schema) return [];

  return Object.entries(props.schema).map(([key, value]: [string, any], index) => {
    const constraints = [];

    if (value.minLength !== undefined) constraints.push(`最小长度: ${value.minLength}`);
    if (value.maxLength !== undefined) constraints.push(`最大长度: ${value.maxLength}`);
    if (value.minimum !== undefined) constraints.push(`最小值: ${value.minimum}`);
    if (value.maximum !== undefined) constraints.push(`最大值: ${value.maximum}`);
    if (value.pattern) constraints.push(`模式: ${value.pattern}`);
    if (value.enum) constraints.push(`枚举: ${value.enum.join(', ')}`);

    return {
      key: index,
      name: key,
      type: value.type || '未知',
      required: value.required || false,
      description: value.description || '无描述',
      constraints,
    };
  });
});

// 树形视图数据
const treeData = computed(() => {
  if (!props.schema) return [];

  const children = Object.entries(props.schema).map(([key, value]: [string, any], index) => {
    const nodeTitle = `${key}${value.description ? ': ' + value.description : ''}`;

    return {
      key: `field-${index}`,
      title: nodeTitle,
      type: value.type || '未知',
      required: value.required || false,
      children: getConstraintsAsChildren(value),
    };
  });

  return [
    {
      key: 'root',
      title: '数据模式',
      children,
    },
  ];
});

// 将约束条件转换为子节点
const getConstraintsAsChildren = (field: any) => {
  const children = [];

  if (field.minLength !== undefined) {
    children.push({
      key: `${field.name}-minLength`,
      title: `最小长度: ${field.minLength}`,
    });
  }

  if (field.maxLength !== undefined) {
    children.push({
      key: `${field.name}-maxLength`,
      title: `最大长度: ${field.maxLength}`,
    });
  }

  if (field.minimum !== undefined) {
    children.push({
      key: `${field.name}-minimum`,
      title: `最小值: ${field.minimum}`,
    });
  }

  if (field.maximum !== undefined) {
    children.push({
      key: `${field.name}-maximum`,
      title: `最大值: ${field.maximum}`,
    });
  }

  if (field.pattern) {
    children.push({
      key: `${field.name}-pattern`,
      title: `模式: ${field.pattern}`,
    });
  }

  if (field.enum) {
    children.push({
      key: `${field.name}-enum`,
      title: `枚举值: ${field.enum.join(', ')}`,
    });
  }

  return children.length > 0 ? children : undefined;
};
</script>

<style scoped>
.schema-visualization {
  margin-top: 16px;
}

.schema-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-view,
.tree-view {
  margin-top: 16px;
}

.tree-view {
  max-height: 300px;
  overflow: auto;
}
</style>
