<template>
  <a-tooltip :title="description" placement="topLeft">
    <info-circle-outlined class="info-icon" />
  </a-tooltip>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';

defineProps({
  description: {
    type: String,
    required: true,
  },
});
</script>

<style scoped>
.info-icon {
  color: #1890ff;
  margin-left: 4px;
  cursor: pointer;
}
</style>
