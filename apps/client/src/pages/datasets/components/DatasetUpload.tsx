import { defineComponent, ref, reactive } from 'vue';
import { Modal, Upload, Form, Input, Select, Button, message, Steps, Card, Typography, Row, Col, Progress } from 'ant-design-vue';
import { InboxOutlined, CloudUploadOutlined, CheckCircleOutlined, LoadingOutlined } from '@ant-design/icons-vue';
import { useDatasetStore, ModelTypeEnum } from '@/store/modules/dataset.store';

const { Title, Text } = Typography;
const { Option } = Select;
const { Step } = Steps;
const { Dragger } = Upload;

export default defineComponent({
  name: 'DatasetUpload',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    const datasetStore = useDatasetStore();
    const currentStep = ref(0);
    const uploading = ref(false);
    const uploadProgress = ref(0);
    const createdDatasetId = ref('');

    const formData = reactive({
      name: '',
      description: '',
      modelType: '',
      tags: [],
    });

    const fileList = ref([]);

    // 模型类型选项，与后端枚举保持一致
    const modelTypes = [
      { value: ModelTypeEnum.INNOVATION_HEALTH_MODEL, label: '科创健康性模型' },
      { value: ModelTypeEnum.GENERIC_RISK_MODEL, label: '内控风险模型' },
    ];

    const handleClose = () => {
      emit('update:visible', false);
      resetForm();
    };

    const resetForm = () => {
      currentStep.value = 0;
      uploading.value = false;
      uploadProgress.value = 0;
      createdDatasetId.value = '';
      Object.assign(formData, {
        name: '',
        description: '',
        modelType: '',
        tags: [],
      });
      fileList.value = [];
    };

    const handleNext = () => {
      if (currentStep.value === 0 && fileList.value.length === 0) {
        message.error('请先选择文件');
        return;
      }
      if (currentStep.value === 1) {
        if (!formData.name || !formData.modelType) {
          message.error('请填写必要信息');
          return;
        }
      }
      currentStep.value++;
    };

    const handlePrev = () => {
      currentStep.value--;
    };

    // 第一步：创建数据集
    const handleCreateDataset = async () => {
      if (!formData.name || !formData.modelType) {
        message.error('请填写必要信息');
        return;
      }

      uploading.value = true;
      uploadProgress.value = 10;

      try {
        const result = await datasetStore.createDataset({
          name: formData.name,
          description: formData.description,
          modelType: formData.modelType as ModelTypeEnum,
          tags: formData.tags,
        });

        createdDatasetId.value = result.id;
        uploadProgress.value = 30;
        currentStep.value = 2; // 跳到上传文件步骤

        message.success('数据集创建成功');
      } catch (error) {
        message.error('创建数据集失败：' + error.message);
      } finally {
        uploading.value = false;
      }
    };

    // 第二步：上传文件
    const handleUploadFile = async () => {
      if (!createdDatasetId.value) {
        message.error('请先创建数据集');
        return;
      }

      if (fileList.value.length === 0) {
        message.error('请选择文件');
        return;
      }

      uploading.value = true;
      uploadProgress.value = 30;

      try {
        // 模拟上传进度
        const progressInterval = setInterval(() => {
          if (uploadProgress.value < 90) {
            uploadProgress.value += 10;
          }
        }, 200);

        await datasetStore.uploadDatasetFile(createdDatasetId.value, fileList.value[0].originFileObj || fileList.value[0]);

        clearInterval(progressInterval);
        uploadProgress.value = 100;

        setTimeout(() => {
          currentStep.value = 3; // 跳到完成步骤
          emit('success');
        }, 500);
      } catch (error) {
        message.error('文件上传失败：' + error.message);
      } finally {
        uploading.value = false;
      }
    };

    const uploadProps = {
      name: 'file',
      multiple: false,
      accept: '.csv,.xlsx,.xls',
      beforeUpload: (file) => {
        const isValidType =
          file.type === 'text/csv' ||
          file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
          file.type === 'application/vnd.ms-excel';

        if (!isValidType) {
          message.error('只支持 CSV 和 Excel 文件格式');
          return false;
        }

        const isLt100M = file.size / 1024 / 1024 < 100;
        if (!isLt100M) {
          message.error('文件大小不能超过 100MB');
          return false;
        }

        fileList.value = [file];
        return false; // 阻止自动上传
      },
      onRemove: () => {
        fileList.value = [];
      },
    };

    return () => (
      <Modal title="上传数据集" visible={props.visible} onCancel={handleClose} footer={null} width={800} destroyOnClose>
        <Steps current={currentStep.value} class="mb-6">
          <Step title="选择文件" icon={<CloudUploadOutlined />} />
          <Step title="填写信息" icon={<InboxOutlined />} />
          <Step title="上传确认" icon={uploading.value ? <LoadingOutlined /> : undefined} />
          <Step title="完成" icon={<CheckCircleOutlined />} />
        </Steps>

        {/* 步骤1：选择文件 */}
        {currentStep.value === 0 && (
          <Card>
            <Dragger {...uploadProps} fileList={fileList.value}>
              <p class="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p class="ant-upload-hint">支持 CSV、Excel 格式，文件大小不超过 100MB</p>
            </Dragger>
            <div class="mt-4 text-right">
              <Button type="primary" onClick={handleNext} disabled={fileList.value.length === 0}>
                下一步
              </Button>
            </div>
          </Card>
        )}

        {/* 步骤2：填写信息 */}
        {currentStep.value === 1 && (
          <Card>
            <Form layout="vertical">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="数据集名称" required>
                    <Input
                      value={formData.name}
                      onChange={(e) => (formData.name = e.target.value)}
                      placeholder="请输入数据集名称"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="模型类型" required>
                    <Select
                      value={formData.modelType}
                      onChange={(value) => (formData.modelType = value)}
                      placeholder="请选择模型类型"
                    >
                      {modelTypes.map((type) => (
                        <Option key={type.value} value={type.value}>
                          {type.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item label="描述">
                <Input.TextArea
                  value={formData.description}
                  onChange={(e) => (formData.description = e.target.value)}
                  placeholder="请输入数据集描述"
                  rows={4}
                />
              </Form.Item>
            </Form>
            <div class="text-right">
              <Button onClick={handlePrev} class="mr-2">
                上一步
              </Button>
              <Button type="primary" onClick={handleCreateDataset} loading={uploading.value}>
                创建数据集
              </Button>
            </div>
          </Card>
        )}

        {/* 步骤3：上传确认 */}
        {currentStep.value === 2 && (
          <Card>
            <div class="mb-4">
              <Title level={4}>确认上传信息</Title>
            </div>
            <Row gutter={16}>
              <Col span={12}>
                <div class="mb-2">
                  <Text strong>文件名：</Text>
                  <Text>{fileList.value[0]?.name}</Text>
                </div>
                <div class="mb-2">
                  <Text strong>数据集名称：</Text>
                  <Text>{formData.name}</Text>
                </div>
                <div class="mb-2">
                  <Text strong>模型类型：</Text>
                  <Text>{modelTypes.find((t) => t.value === formData.modelType)?.label}</Text>
                </div>
              </Col>
              <Col span={12}>
                <div class="mb-2">
                  <Text strong>文件大小：</Text>
                  <Text>{(fileList.value[0]?.size / 1024 / 1024).toFixed(2)} MB</Text>
                </div>
                <div class="mb-2">
                  <Text strong>描述：</Text>
                  <Text>{formData.description || '无'}</Text>
                </div>
              </Col>
            </Row>

            {uploading.value && (
              <div class="mt-4">
                <Progress percent={uploadProgress.value} status="active" />
                <Text type="secondary">正在上传...</Text>
              </div>
            )}

            <div class="mt-4 text-right">
              <Button onClick={handlePrev} disabled={uploading.value} class="mr-2">
                上一步
              </Button>
              <Button type="primary" onClick={handleUploadFile} loading={uploading.value}>
                上传文件
              </Button>
            </div>
          </Card>
        )}

        {/* 步骤4：完成 */}
        {currentStep.value === 3 && (
          <Card class="text-center">
            <CheckCircleOutlined style={{ fontSize: '48px', color: '#52c41a' }} />
            <div class="mt-4">
              <Title level={3}>上传成功！</Title>
              <Text type="secondary">数据集已成功上传，系统正在处理中...</Text>
            </div>
            <div class="mt-6">
              <Button type="primary" onClick={handleClose}>
                完成
              </Button>
            </div>
          </Card>
        )}
      </Modal>
    );
  },
});
