import { defineComponent } from 'vue';
import { Button } from 'ant-design-vue';
import './index.less';

export default defineComponent({
  name: 'NotFoundPage',
  setup(props, { root }) {
    const goHome = () => {
      root.$router.push('/');
    };

    return () => (
      <div class="not-found-container">
        <div class="not-found-content">
          <h1 class="not-found-title">404</h1>
          <p class="not-found-desc">抱歉，您访问的页面不存在</p>
          <Button type="primary" onClick={goHome}>
            返回首页
          </Button>
        </div>
      </div>
    );
  },
});
