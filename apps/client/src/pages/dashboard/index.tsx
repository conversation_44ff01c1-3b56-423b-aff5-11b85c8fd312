import { defineComponent, ref, reactive, onMounted } from 'vue';
import { DatabaseOutlined, ExperimentOutlined, AppstoreOutlined, CloudServerOutlined } from '@ant-design/icons-vue';
import './index.less';

export default defineComponent({
  name: 'DashboardPage',
  setup(props, { root }) {
    const loading = ref(true);

    // 模拟数据
    const statistics = reactive({
      datasets: 0,
      trainingJobs: 0,
      models: 0,
      deployments: 0,
    });

    const systemStatus = reactive({
      cpuUsage: 0,
      memoryUsage: 0,
      diskUsage: 0,
    });

    const recentTrainingJobs = ref([]);

    const trainingColumns = [
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '模型类型',
        dataIndex: 'modelType',
        key: 'modelType',
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
      },
      {
        title: '操作',
        key: 'action',
      },
    ];

    const getStatusColor = (status) => {
      const statusMap = {
        COMPLETED: 'success',
        RUNNING: 'processing',
        FAILED: 'error',
        PENDING: 'warning',
        STOPPED: 'default',
      };
      return statusMap[status] || 'default';
    };

    const getResourceColor = (usage) => {
      if (usage >= 90) return '#f5222d';
      if (usage >= 70) return '#faad14';
      return '#52c41a';
    };

    const viewTrainingJob = (id) => {
      root.$router.push(`/training/${id}`);
    };

    // 模拟加载数据
    onMounted(() => {
      setTimeout(() => {
        statistics.datasets = 24;
        statistics.trainingJobs = 156;
        statistics.models = 42;
        statistics.deployments = 18;

        systemStatus.cpuUsage = 45;
        systemStatus.memoryUsage = 62;
        systemStatus.diskUsage = 78;

        recentTrainingJobs.value = [
          { id: '1', name: '科创健康性模型训练-001', modelType: '科创健康性模型', status: 'COMPLETED' },
          { id: '2', name: '内控风险模型训练-002', modelType: '内控风险模型', status: 'RUNNING' },
          { id: '3', name: '科创健康性模型训练-003', modelType: '科创健康性模型', status: 'FAILED' },
          { id: '4', name: '内控风险模型训练-004', modelType: '内控风险模型', status: 'PENDING' },
          { id: '5', name: '科创健康性模型训练-005', modelType: '科创健康性模型', status: 'COMPLETED' },
        ];

        loading.value = false;
      }, 1000);
    });

    return () => (
      <div class="dashboard-container">
        <a-row gutter={[16, 16]}>
          <a-col xs={24} sm={24} md={24} lg={24} xl={24}>
            <a-card title="平台概览" bordered={false}>
              <a-row gutter={16}>
                <a-col span={6}>
                  <a-statistic title="数据集" value={statistics.datasets} loading={loading.value}>
                    <DatabaseOutlined slot="prefix" />
                  </a-statistic>
                </a-col>
                <a-col span={6}>
                  <a-statistic title="训练任务" value={statistics.trainingJobs} loading={loading.value}>
                    <ExperimentOutlined slot="prefix" />
                  </a-statistic>
                </a-col>
                <a-col span={6}>
                  <a-statistic title="模型" value={statistics.models} loading={loading.value}>
                    <AppstoreOutlined slot="prefix" />
                  </a-statistic>
                </a-col>
                <a-col span={6}>
                  <a-statistic title="已部署模型" value={statistics.deployments} loading={loading.value}>
                    <CloudServerOutlined slot="prefix" />
                  </a-statistic>
                </a-col>
              </a-row>
            </a-card>
          </a-col>

          <a-col xs={24} sm={24} md={16} lg={16} xl={16}>
            <a-card title="最近训练任务" bordered={false}>
              <a-table
                dataSource={recentTrainingJobs.value}
                columns={trainingColumns}
                pagination={false}
                loading={loading.value}
                size="small"
                scopedSlots={{
                  status: (text, record) => <a-tag color={getStatusColor(record.status)}>{record.status}</a-tag>,
                  action: (text, record) => (
                    <a-button type="link" size="small" onClick={() => viewTrainingJob(record.id)}>
                      查看
                    </a-button>
                  ),
                }}
              />
            </a-card>
          </a-col>

          <a-col xs={24} sm={24} md={8} lg={8} xl={8}>
            <a-card title="系统状态" bordered={false}>
              <a-row gutter={[0, 16]}>
                <a-col span={24}>
                  <a-progress
                    percent={systemStatus.cpuUsage}
                    strokeColor={getResourceColor(systemStatus.cpuUsage)}
                    size="small"
                  />
                  <div class="resource-label">CPU 使用率: {systemStatus.cpuUsage}%</div>
                </a-col>
                <a-col span={24}>
                  <a-progress
                    percent={systemStatus.memoryUsage}
                    strokeColor={getResourceColor(systemStatus.memoryUsage)}
                    size="small"
                  />
                  <div class="resource-label">内存使用率: {systemStatus.memoryUsage}%</div>
                </a-col>
                <a-col span={24}>
                  <a-progress
                    percent={systemStatus.diskUsage}
                    strokeColor={getResourceColor(systemStatus.diskUsage)}
                    size="small"
                  />
                  <div class="resource-label">磁盘使用率: {systemStatus.diskUsage}%</div>
                </a-col>
              </a-row>
            </a-card>
          </a-col>
        </a-row>
      </div>
    );
  },
});
