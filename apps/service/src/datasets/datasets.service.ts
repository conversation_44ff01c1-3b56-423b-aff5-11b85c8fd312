import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DatasetEntity, DatasetStatus, HealthModelDatasetEntity, RiskModelDatasetEntity } from '../entities';
import { StorageService } from '../common/storage.service';
import { CreateDatasetDto } from './dto/create-dataset.dto';
import { UpdateDatasetDto } from './dto/update-dataset.dto';
import { DatasetFiltersDto } from './dto/dataset-filters.dto';
import * as fs from 'fs';
import * as path from 'path';
import * as csv from 'csv-parser';
import { SearchDatasetItemDto } from './dto/search-dataset-item.dto';
import { PaginationResponse } from 'src/common/PaginationResponse';
import { chunk } from 'lodash';
import * as Bluebird from 'bluebird';
import { ModelTypeEnum } from 'src/common/model-type.enum';
import { SearchDatasetResponse } from './dto/search-dataset.response';

@Injectable()
export class DatasetsService {
  constructor(
    @InjectRepository(DatasetEntity)
    private readonly datasetsRepository: Repository<DatasetEntity>,
    @InjectRepository(HealthModelDatasetEntity)
    private readonly healthModelDatasetsRepository: Repository<HealthModelDatasetEntity>,
    @InjectRepository(RiskModelDatasetEntity)
    private readonly riskModelDatasetsRepository: Repository<RiskModelDatasetEntity>,
    private readonly storageService: StorageService
  ) {}

  /**
   * 将数据集导出为CSV文件
   * @param dataset 数据集实体
   * @returns CSV文件路径
   */
  async exportDatasetToCsv(dataset: DatasetEntity | null): Promise<string> {
    if (!dataset) {
      throw new BadRequestException('数据集不存在');
    }
    try {
      // 确保临时目录存在
      const tmpDir = path.join(process.cwd(), 'uploads', 'temp');
      if (!fs.existsSync(tmpDir)) {
        fs.mkdirSync(tmpDir, { recursive: true });
      }

      // 生成CSV文件路径
      const csvFileName = `${dataset.modelType}_${dataset.name}_${dataset.id}_${Date.now()}.csv`;
      const csvFilePath = path.join(tmpDir, csvFileName);

      // 获取数据集的所有数据
      const dataSetItems = await this.searchDatasetItems(dataset.id, {
        pageIndex: 1,
        pageSize: dataset.rowCount,
      });

      if (!dataSetItems.data || dataSetItems.data.length === 0) {
        throw new BadRequestException('数据集为空，无法导出');
      }

      // 生成CSV内容
      const csvContent = this.generateCsvContent(dataSetItems.data, dataset.schema);

      // 写入文件
      fs.writeFileSync(csvFilePath, csvContent, 'utf8');

      return csvFilePath;
    } catch (error) {
      console.error('导出CSV文件失败:', error);
      throw new BadRequestException(`导出CSV文件失败: ${error.message}`);
    }
  }

  /**
   * 生成CSV内容
   * @param data 数据数组
   * @param schema 数据模式
   * @returns CSV格式的字符串
   */
  private generateCsvContent(data: any[], schema: Record<string, any>): string {
    if (!data || data.length === 0) {
      return '';
    }

    // 获取所有字段名（按schema顺序）
    const fieldNames = Object.keys(schema);

    // 生成表头
    const header = fieldNames.map((field) => this.escapeCsvValue(field)).join(',');

    // 生成数据行
    const rows = data.map((item) => {
      const row = fieldNames.map((field) => {
        const value = item[this.covertFieldName(field)];
        return this.escapeCsvValue(value);
      });
      return row.join(',');
    });

    // 组合表头和数据行
    return [header, ...rows].join('\n');
  }

  /**
   * 蛇形名字转驼峰
   * @param fieldName 蛇形名字
   * @returns 驼峰名字
   */
  private covertFieldName(fieldName: string): string {
    return fieldName.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  }

  /**
   * 转义CSV字段值
   * @param value 字段值
   * @returns 转义后的值
   */
  private escapeCsvValue(value: any): string {
    if (value === null || value === undefined) {
      return '';
    }

    const stringValue = String(value);

    // 如果值包含逗号、引号或换行符，需要用引号包围并转义内部引号
    if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n') || stringValue.includes('\r')) {
      // 将内部的双引号替换为两个双引号
      const escapedValue = stringValue.replace(/"/g, '""');
      return `"${escapedValue}"`;
    }

    return stringValue;
  }

  /**
   * 创建数据集元数据
   */
  async create(createDatasetDto: CreateDatasetDto, userId: string): Promise<DatasetEntity> {
    const dataset = this.datasetsRepository.create({
      ...createDatasetDto,
      createdBy: userId,
      status: DatasetStatus.UPLOADING,
      fileSize: 0,
      rowCount: 0,
      columnCount: 0,
      schema: {},
    });

    return this.datasetsRepository.save(dataset);
  }

  /**
   * 查找所有数据集
   */
  async findAll(filters: DatasetFiltersDto): Promise<SearchDatasetResponse> {
    filters.pageIndex = filters.pageIndex || 1;
    filters.pageSize = filters.pageSize || 10;
    const { pageIndex, pageSize } = filters;
    const query = this.datasetsRepository.createQueryBuilder('dataset');

    if (filters.modelType) {
      query.andWhere('dataset.modelType = :modelType', { modelType: filters.modelType });
    }

    if (filters.status) {
      query.andWhere('dataset.status = :status', { status: filters.status });
    }

    if (filters.createdBy) {
      query.andWhere('dataset.createdBy = :createdBy', { createdBy: filters.createdBy });
    }

    if (filters.search) {
      query.andWhere('(dataset.name LIKE :search OR dataset.description LIKE :search)', {
        search: `%${filters.search}%`,
      });
    }

    query.orderBy('dataset.createdAt', filters.sortOrder || 'DESC');

    query.skip((pageIndex - 1) * pageSize).take(pageSize);

    const [datasets, total] = await query.getManyAndCount();
    return {
      data: datasets,
      total,
      pageSize,
      pageIndex,
    };
  }

  /**
   * 查找单个数据集
   */
  async findOne(id: string): Promise<DatasetEntity> {
    const dataset = await this.datasetsRepository.findOne({ where: { id } });
    if (!dataset) {
      throw new NotFoundException(`Dataset with ID ${id} not found`);
    }
    return dataset;
  }

  /**
   * 更新数据集
   */
  async update(id: string, updateDatasetDto: UpdateDatasetDto): Promise<DatasetEntity> {
    const dataset = await this.findOne(id);

    // 只允许更新某些字段
    if (updateDatasetDto.name) dataset.name = updateDatasetDto.name;
    if (updateDatasetDto.description) dataset.description = updateDatasetDto.description;
    if (updateDatasetDto.tags) dataset.tags = updateDatasetDto.tags;

    return this.datasetsRepository.save(dataset);
  }

  /**
   * 删除数据集
   */
  async remove(id: string): Promise<void> {
    const dataset = await this.findOne(id);

    // 删除存储中的文件
    try {
      const filePath = `datasets/${dataset.modelType}/${id}.csv`;
      await this.storageService.deleteFile(filePath);
    } catch (error) {
      console.error('Error deleting dataset file:', error);
      // 继续删除数据库记录，即使文件删除失败
    }

    await this.datasetsRepository.remove(dataset);
  }

  /**
   * 获取数据集预览
   */
  async searchDatasetItems(
    id: string,
    body: SearchDatasetItemDto
  ): Promise<PaginationResponse<HealthModelDatasetEntity | RiskModelDatasetEntity>> {
    body.pageIndex = body.pageIndex || 1;
    body.pageSize = body.pageSize || 10;
    const { pageSize, pageIndex } = body;
    const dataset = await this.findOne(id);

    // 根据模型类型获取具体数据
    let data: HealthModelDatasetEntity[] | RiskModelDatasetEntity[] = [];

    if (dataset.modelType === ModelTypeEnum.INNOVATION_HEALTH_MODEL) {
      const healthData = await this.healthModelDatasetsRepository.find({
        where: { datasetId: id },
        take: pageSize,
        skip: (pageIndex - 1) * pageSize,
      });
      data = healthData;
    } else if (dataset.modelType === ModelTypeEnum.GENERIC_RISK_MODEL) {
      const riskData = await this.riskModelDatasetsRepository.find({
        where: { datasetId: id },
        take: pageSize,
        skip: (pageIndex - 1) * pageSize,
      });
      data = riskData;
    } else {
      throw new BadRequestException(`Unsupported model type: ${dataset.modelType}`);
    }

    return {
      pageIndex,
      pageSize,
      total: dataset.rowCount,
      data,
    };
  }

  /**
   * 处理上传的CSV文件
   */
  async processUploadedFile(id: string, file: Express.Multer.File, userId: string, badCase: string): Promise<DatasetEntity> {
    const dataset = await this.findOne(id);

    if (dataset.createdBy !== userId) {
      throw new BadRequestException('You do not have permission to upload to this dataset');
    }

    // 更新状态为处理中
    dataset.status = DatasetStatus.PROCESSING;
    await this.datasetsRepository.save(dataset);

    try {
      // 处理CSV文件
      const results = await this.parseCSV(file.path);

      // 提取模式信息
      const schema = this.extractSchema(results);

      // 更新数据集元数据
      dataset.fileSize = file.size;
      dataset.rowCount = results.length;
      dataset.columnCount = Object.keys(schema).length;
      dataset.schema = schema;

      // 根据模型类型处理数据
      await this.processDataByModelType(dataset, results, badCase);

      // 更新状态为就绪
      dataset.status = DatasetStatus.READY;
      return this.datasetsRepository.save(dataset);
    } catch (error) {
      // 更新状态为错误
      dataset.status = DatasetStatus.ERROR;
      await this.datasetsRepository.save(dataset);
      throw error;
    } finally {
      // 清理临时文件
      try {
        fs.unlinkSync(file.path);
      } catch (error) {
        console.error('Error deleting temporary file:', error);
      }
    }
  }

  /**
   * 解析CSV文件
   */
  private async parseCSV(filePath: string): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const results: any[] = [];

      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (data) => results.push(data))
        .on('end', () => resolve(results))
        .on('error', (error) => reject(error));
    });
  }

  /**
   * 提取数据模式
   */
  private extractSchema(data: any[]): Record<string, any> {
    if (data.length === 0) {
      return {};
    }

    const firstRow = data[0];
    const schema: Record<string, any> = {};

    for (const key of Object.keys(firstRow)) {
      // 推断字段类型
      const values = data.slice(0, Math.min(100, data.length)).map((row) => row[key]);
      schema[key] = this.inferFieldType(values, key);
    }

    return schema;
  }

  /**
   * 推断字段类型
   */
  private inferFieldType(values: any[], fieldName: string): any {
    // 移除空值
    const nonEmptyValues = values.filter((v) => v !== null && v !== undefined && v !== '');

    if (nonEmptyValues.length === 0) {
      return { type: 'string', nullable: true };
    }

    // 检查是否为数字
    const numericValues = nonEmptyValues.filter((v) => !isNaN(Number(v)));
    if (numericValues.length === nonEmptyValues.length) {
      // 检查是否为整数
      const intValues = numericValues.filter((v) => Number.isInteger(Number(v)));
      if (intValues.length === numericValues.length) {
        return { type: 'integer', nullable: values.length > nonEmptyValues.length };
      }
      return { type: 'number', nullable: values.length > nonEmptyValues.length };
    }

    // 检查是否为日期
    const datePattern = /^\\d{4}[-/]\\d{1,2}[-/]\\d{1,2}$/;
    const dateValues = nonEmptyValues.filter((v) => datePattern.test(String(v)));
    if (dateValues.length === nonEmptyValues.length) {
      return { type: 'date', nullable: values.length > nonEmptyValues.length };
    }

    // 默认为字符串
    return { type: 'string', nullable: values.length > nonEmptyValues.length };
  }

  /**
   * 根据模型类型处理数据
   */
  private async processDataByModelType(dataset: DatasetEntity, data: any[], isBadCase: string): Promise<void> {
    if (dataset.modelType === 'health_model') {
      await this.processHealthModelData(dataset, data, isBadCase);
    } else if (dataset.modelType === 'risk_model') {
      await this.processRiskModelData(dataset, data);
    } else {
      throw new BadRequestException(`Unsupported model type: ${dataset.modelType}`);
    }
  }

  /**
   * 处理科创健康性模型数据
   */
  private async processHealthModelData(dataset: DatasetEntity, data: any[], isBadCase: string): Promise<void> {
    // 清除旧数据
    await this.healthModelDatasetsRepository.delete({ datasetId: dataset.id });

    // 批量插入新数据
    const healthData = data.map((row) => {
      const entity = new HealthModelDatasetEntity();
      entity.datasetId = dataset.id;

      // 企业基本信息
      entity.enterpriseId = row.enterprise_id || row.enterpriseId;
      entity.enterpriseName = row.enterprise_name || row.enterpriseName;

      // 科创健康性指标
      entity.comprehensiveScore = this.parseNumber(row.comprehensive_score);
      entity.label = isBadCase ? 1 : row.label ? this.parseNumber(row.label) : null;

      // 技术专利指标
      entity.techPatentApplicationRatio = this.parseNumber(row.tech_patent_application_ratio);
      entity.techPatentRejectionRate = this.parseNumber(row.tech_patent_rejection_rate);
      entity.techPatentAuthorizationRate = this.parseNumber(row.tech_patent_authorization_rate);
      entity.techPatentMaintenanceRate = this.parseNumber(row.tech_patent_maintenance_rate);
      entity.techPatentAuthorizationStability = this.parseNumber(row.tech_patent_authorization_stability);
      entity.techPatentAuthorizationRanking = this.parseNumber(row.tech_patent_authorization_ranking);
      entity.techSoftwareCopyrightRanking = this.parseNumber(row.tech_software_copyright_ranking);
      entity.techPatentConcentration = this.parseNumber(row.tech_patent_concentration);
      entity.techExternalPatentRatio = this.parseNumber(row.tech_external_patent_ratio);
      entity.techPatentContinuity = this.parseNumber(row.tech_patent_continuity);
      entity.techAdjPatentOutflow = this.parseNumber(row.tech_adj_patent_outflow);
      entity.techAdjPctPatent = this.parseNumber(row.tech_adj_pct_patent);
      entity.techAdjIpPledge = this.parseNumber(row.tech_adj_ip_pledge);
      entity.techAdjIpTransformation = this.parseNumber(row.tech_adj_ip_transformation);
      entity.techAdjTechAchievement = this.parseNumber(row.tech_adj_tech_achievement);

      // 发展指标
      entity.devTalentStability = this.parseNumber(row.dev_talent_stability);
      entity.devEquityFinancing = this.parseNumber(row.dev_equity_financing);
      entity.devEnterpriseHonor = this.parseNumber(row.dev_enterprise_honor);
      entity.devAdjEmployeeShareholding = this.parseNumber(row.dev_adj_employee_shareholding);
      entity.devAdjHonorCancellation = this.parseNumber(row.dev_adj_honor_cancellation);

      // 运营指标
      entity.operCapitalPaidRatio = this.parseNumber(row.oper_capital_paid_ratio);
      entity.operKeyPersonnelChange = this.parseNumber(row.oper_key_personnel_change);
      entity.operEquityChangeFrequency = this.parseNumber(row.oper_equity_change_frequency);
      entity.operAdjCapitalReduction = this.parseNumber(row.oper_adj_capital_reduction);
      entity.operAdjEquityStructure = this.parseNumber(row.oper_adj_equity_structure);
      entity.operAdjRelatedPartyChange = this.parseNumber(row.oper_adj_related_party_change);
      entity.operAdjBusinessStatus = this.parseNumber(row.oper_adj_business_status);
      entity.operAdjRevocation = this.parseNumber(row.oper_adj_revocation);

      // 风险指标
      entity.riskAdjExecutionRestriction = this.parseNumber(row.risk_adj_execution_restriction);
      entity.riskAdjFinancialLitigation = this.parseNumber(row.risk_adj_financial_litigation);
      entity.riskAdjEnvironmentalPenalty = this.parseNumber(row.risk_adj_environmental_penalty);
      entity.riskAdjTaxArrears = this.parseNumber(row.risk_adj_tax_arrears);

      // 存储其他字段
      const additionalFields: Record<string, any> = {};
      for (const key of Object.keys(row)) {
        if (!Object.keys(entity).includes(this.camelToSnake(key)) && !Object.keys(entity).includes(key)) {
          additionalFields[key] = row[key];
        }
      }

      return entity;
    });

    const chunks = chunk(healthData, 100);
    await Bluebird.map(chunks, (chunkItems) => this.healthModelDatasetsRepository.save(chunkItems), { concurrency: 3 });
  }

  /**
   * 将驼峰命名转换为蛇形命名
   */
  private camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);
  }

  /**
   * 处理内控风险模型数据
   */
  private async processRiskModelData(dataset: DatasetEntity, data: any[]): Promise<void> {
    // 清除旧数据
    await this.riskModelDatasetsRepository.delete({ datasetId: dataset.id });

    // 批量插入新数据
    const riskData = data.map((row) => {
      const entity = new RiskModelDatasetEntity();
      entity.datasetId = dataset.id;
      entity.enterpriseId = row.enterprise_id || row.enterpriseId;
      entity.enterpriseName = row.enterprise_name || row.enterpriseName;
      entity.riskScore = this.parseNumber(row.risk_score || row.riskScore);
      entity.riskLevel = row.risk_level || row.riskLevel;
      entity.financialHealth = row.financial_health || row.financialHealth;
      entity.complianceStatus = row.compliance_status || row.complianceStatus;
      entity.auditResult = row.audit_result || row.auditResult;

      // 存储其他字段
      const additionalFields: Record<string, any> = {};
      for (const key of Object.keys(row)) {
        if (
          ![
            'enterprise_id',
            'enterpriseId',
            'enterprise_name',
            'enterpriseName',
            'risk_score',
            'riskScore',
            'risk_level',
            'riskLevel',
            'financial_health',
            'financialHealth',
            'compliance_status',
            'complianceStatus',
            'audit_result',
            'auditResult',
          ].includes(key)
        ) {
          additionalFields[key] = row[key];
        }
      }
      entity.additionalFields = additionalFields;

      return entity;
    });

    await this.riskModelDatasetsRepository.save(riskData);
  }

  /**
   * 解析数字
   */
  private parseNumber(value: any): number | null {
    if (value === null || value === undefined || value === '') {
      return null;
    }
    const num = Number(value);
    return isNaN(num) ? null : num;
  }

  /**
   * 获取数据集模式
   */
  async getDatasetSchema(modelType: ModelTypeEnum): Promise<any> {
    // 返回不同模型类型的数据模式定义
    if (modelType === ModelTypeEnum.INNOVATION_HEALTH_MODEL) {
      return {
        type: 'object',
        required: ['companyName', 'industryCode'],
        properties: {
          companyName: { type: 'string', description: '公司名称' },
          registrationCapital: { type: 'number', description: '注册资本' },
          establishmentDate: { type: 'string', format: 'date', description: '成立日期' },
          industryCode: { type: 'string', description: '行业代码' },
          employeeCount: { type: 'integer', description: '员工数量' },
          patentCount: { type: 'integer', description: '专利数量' },
        },
      };
    } else if (modelType === ModelTypeEnum.GENERIC_RISK_MODEL) {
      return {
        type: 'object',
        required: ['enterprise_id', 'enterprise_name', 'risk_score', 'risk_level'],
        properties: {
          // 企业基本信息
          enterprise_id: { type: 'string', description: '企业ID' },
          enterprise_name: { type: 'string', description: '企业名称' },

          // 风险评估指标
          risk_score: { type: 'number', description: '风险评分' },
          risk_level: { type: 'string', description: '风险等级' },
          financial_health: { type: 'string', description: '财务健康状况' },
          compliance_status: { type: 'string', description: '合规状态' },
          audit_result: { type: 'string', description: '审计结果' },
        },
      };
    } else {
      throw new BadRequestException(`Unsupported model type: ${modelType}`);
    }
  }
}
